* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  background-color: #212121;
  color: #ffffff;
  min-height: 100vh;
}

#app {
  min-height: 100vh;
}

/* Navigation Styles */
.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background-color: #212121;
  height: 80px;
  box-sizing: border-box;
}

.logo {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  font-family: "Holtwood One SC", serif;
  font-size: 24px;
  font-weight: 400;
  color: #ffffff;
  height: 90px;
}

.logo-icon {
  width: 90px;
  height: 90px;
  background-image: url("/logo.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.nav-tabs {
  display: flex;
  align-items: flex-end;
  gap: 40px;
}

.nav-tab {
  font-size: 20px;
  font-weight: bold;
  color: #888;
  cursor: pointer;
  transition: color 0.3s ease;

  margin-top: 30px;
}

.nav-tab.active {
  color: #16d62c;
}

.nav-tab:hover {
  color: #ffffff;
}

.nav-buttons {
  display: flex;
  align-items: flex-end;
  gap: 10px;
}

.search-container {
  position: relative;
  margin-top: 5px;
}

.search-input {
  width: 150px;
  height: 36px;
  background-color: #333;
  border: 2px solid #555;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  padding: 8px 40px 8px 12px;
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #16d62c;
  background-color: #3a3a3a;
}

.search-input::placeholder {
  color: #888;
}

.search-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: #888;
  pointer-events: none;
}

.sort-button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  padding: 8px;
  transition: all 0.3s ease;
}

.sort-button:hover {
  color: #16d62c;
}

.sort-icon {
  width: 30px;
  height: 30px;
}

.add-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #333;
  border: 2px solid #555;
  color: #16d62c;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  transition: all 0.3s ease;
}

.add-button:hover {
  background-color: #16d62c;
  color: #000;
}

/* Main Content */
.main-content {
  padding: 20px 40px;
  padding-top: 50px;
}

.habits-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  width: calc(100vw - 80px);
  max-width: none;
  margin: 0 auto;
  justify-content: center;
}

/* Habit Card Styles */
.habit-card {
  background-color: #303030;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  min-height: 200px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: grab;
}

.habit-card:active {
  cursor: grabbing;
}

.habit-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  cursor: grabbing;
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  position: relative;
}

.habit-card.dragging * {
  pointer-events: none;
  user-select: none;
}

.habit-card.drag-over {
  border-color: #16d62c;
  background-color: #2d3a2d;
  transform: scale(1.02);
  box-shadow: 0 4px 15px rgba(22, 214, 44, 0.2);
  z-index: 100;
  position: relative;
}

.habit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
}

.drag-handle {
  color: #999;
  font-size: 18px;
  cursor: grab;
  padding: 8px;
  margin-right: 12px;
  user-select: none;
  line-height: 1;
  border-radius: 4px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
}

.drag-handle:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.habit-card.dragging .drag-handle {
  cursor: grabbing;
  color: #16d62c;
}

.habit-title {
  font-size: 22px;
  font-weight: 600;
  color: #ffffff;
}

.habit-title.clickable {
  cursor: pointer;
  transition: color 0.2s ease;
}

.habit-title.clickable:hover {
  color: #16d62c;
}

.habit-check {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  border: none;
  background-color: var(--habit-color-50);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.habit-check.completed {
  background-color: var(--habit-color);
}

.habit-check::before {
  content: "";
  position: absolute;
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2.5'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpolyline points='9,12 11,14 15,10'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.habit-check.completed::before {
  opacity: 1;
}

.habit-grid-container {
  overflow-x: auto;
  margin-top: 10px;
  display: flex;
  gap: 20px;
  padding-bottom: 8px;
}

/* Transparent scrollbar styling for habit cards */
.habit-grid-container::-webkit-scrollbar {
  height: 6px;
}

.habit-grid-container::-webkit-scrollbar-track {
  background: transparent;
}

.habit-grid-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.habit-grid-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.habit-grid {
  display: grid;
  grid-template-columns: repeat(14, 1fr);
  gap: 4px;
  min-width: 280px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.habit-day {
  aspect-ratio: 1;
  border-radius: 4px;
  cursor: default;
  transition: all 0.2s ease;
}

.habit-day.completed {
  background-color: var(--habit-color);
}

.habit-day.incomplete {
  background-color: var(--habit-color-50);
}

.habit-day.today {
  border: 2px solid #fff;
  /* box-shadow: 0 0 8px rgba(255, 255, 255, 0.3); */
  position: relative;
}

.habit-day.today::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  /* border: 1px solid #00ff88; */
  border-radius: 6px;
  pointer-events: none;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: #303030;
  border-radius: 12px;
  padding: 30px;
  width: 90%;
  max-width: 400px;
}

.modal h2 {
  margin-bottom: 20px;
  color: #ffffff;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #ccc;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #555;
  border-radius: 6px;
  background-color: #333;
  color: #fff;
  font-size: 16px;
}

.form-group input:focus {
  outline: none;
  border-color: #16d62c;
}

.color-picker {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;
}

.color-option.selected {
  border-color: #fff;
  transform: scale(1.1);
}

.modal-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 30px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #16d62c;
  color: #000;
}

.btn-primary:hover {
  background-color: #12b024;
}

.btn-secondary {
  background-color: #555;
  color: #fff;
}

.btn-secondary:hover {
  background-color: #666;
}

/* Responsive */
@media (max-width: 1200px) {
  .habits-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .logo-icon {
    width: 70px;
    height: 70px;
  }

  .logo {
    height: 70px;
  }
}

@media (max-width: 768px) {
  .nav-container {
    padding: 15px 20px;
    height: 70px;
  }

  .nav-tabs {
    gap: 20px;
  }

  .main-content {
    padding: 15px 20px;
  }

  .habits-grid {
    grid-template-columns: 1fr;
    width: calc(100vw - 40px);
  }

  .logo-icon {
    width: 60px;
    height: 60px;
  }

  .logo {
    height: 60px;
  }
}
