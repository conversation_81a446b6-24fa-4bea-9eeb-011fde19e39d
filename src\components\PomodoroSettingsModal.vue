<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Settings</h2>
        <button class="close-button" @click="$emit('close')">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>

      <div class="settings-content">
        <!-- Settings Navigation -->
        <div class="settings-nav">
          <button
            class="nav-item"
            :class="{ active: activeTab === 'timer' }"
            @click="activeTab = 'timer'"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"
              />
            </svg>
            Pomodoro Timer
          </button>
          <button
            class="nav-item"
            :class="{ active: activeTab === 'alarm' }"
            @click="activeTab = 'alarm'"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
              />
            </svg>
            Alarm Sound
          </button>
          <button
            class="nav-item"
            :class="{ active: activeTab === 'manual' }"
            @click="activeTab = 'manual'"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z M17,12H12V17H10V12H7V10H10V7H12V10H17V12Z"
              />
            </svg>
            Manual Entry
          </button>
        </div>

        <!-- Timer Settings -->
        <div class="settings-panel" v-if="activeTab === 'timer'">
          <div class="setting-group">
            <label>Pomodoro Length (minutes)</label>
            <div class="input-with-select">
              <input
                type="number"
                v-model.number="settings.pomodoroLength"
                min="1"
                max="120"
                class="setting-input"
                @input="validateInput('pomodoroLength', $event)"
              />
              <select
                v-model="settings.pomodoroLength"
                class="setting-select-small"
              >
                <option value="15">15</option>
                <option value="20">20</option>
                <option value="25">25</option>
                <option value="30">30</option>
                <option value="45">45</option>
                <option value="60">60</option>
              </select>
            </div>
          </div>

          <div class="setting-group">
            <label>Short Break Length (minutes)</label>
            <div class="input-with-select">
              <input
                type="number"
                v-model.number="settings.shortBreakLength"
                min="1"
                max="60"
                class="setting-input"
                @input="validateInput('shortBreakLength', $event)"
              />
              <select
                v-model="settings.shortBreakLength"
                class="setting-select-small"
              >
                <option value="3">3</option>
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="15">15</option>
              </select>
            </div>
          </div>

          <div class="setting-group">
            <label>Long Break Length (minutes)</label>
            <div class="input-with-select">
              <input
                type="number"
                v-model.number="settings.longBreakLength"
                min="1"
                max="120"
                class="setting-input"
                @input="validateInput('longBreakLength', $event)"
              />
              <select
                v-model="settings.longBreakLength"
                class="setting-select-small"
              >
                <option value="10">10</option>
                <option value="15">15</option>
                <option value="20">20</option>
                <option value="30">30</option>
              </select>
            </div>
          </div>

          <div class="setting-group">
            <label>Long Break After</label>
            <select v-model="settings.longBreakAfter" class="setting-select">
              <option value="2">2 Pomodoros</option>
              <option value="3">3 Pomodoros</option>
              <option value="4">4 Pomodoros</option>
              <option value="5">5 Pomodoros</option>
            </select>
          </div>

          <div class="setting-group toggle-group">
            <label>Auto Start Next Pomodoro</label>
            <button
              class="toggle-button"
              :class="{ active: settings.autoStartPomodoro }"
              @click="settings.autoStartPomodoro = !settings.autoStartPomodoro"
            >
              <div class="toggle-slider"></div>
            </button>
          </div>

          <div class="setting-group toggle-group">
            <label>Auto Start Break</label>
            <button
              class="toggle-button"
              :class="{ active: settings.autoStartBreak }"
              @click="settings.autoStartBreak = !settings.autoStartBreak"
            >
              <div class="toggle-slider"></div>
            </button>
          </div>
        </div>

        <!-- Alarm Settings -->
        <div class="settings-panel" v-if="activeTab === 'alarm'">
          <div class="setting-group">
            <label>Pomodoro End Alarm</label>
            <div class="alarm-options">
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    value="beep"
                    v-model="settings.pomodoroAlarm"
                    name="pomodoroAlarm"
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-text">Alarm Beep</span>
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    value="guitar"
                    v-model="settings.pomodoroAlarm"
                    name="pomodoroAlarm"
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-text">Guitar</span>
                </label>
              </div>
              <button class="test-button" @click="testAlarm('pomodoro')">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                </svg>
                Test
              </button>
            </div>
          </div>

          <div class="setting-group">
            <label>Break End Alarm</label>
            <div class="alarm-options">
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    value="beep"
                    v-model="settings.breakAlarm"
                    name="breakAlarm"
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-text">Alarm Beep</span>
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    value="guitar"
                    v-model="settings.breakAlarm"
                    name="breakAlarm"
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-text">Guitar</span>
                </label>
              </div>
              <button class="test-button" @click="testAlarm('break')">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                </svg>
                Test
              </button>
            </div>
          </div>

          <div class="setting-group">
            <label>Alarm Volume</label>
            <div class="volume-control">
              <svg viewBox="0 0 24 24" fill="currentColor" class="volume-icon">
                <path
                  d="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.85 14,18.71V20.77C18.01,19.86 21,16.28 21,12C21,7.72 18.01,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z"
                />
              </svg>
              <input
                type="range"
                v-model.number="settings.alarmVolume"
                min="0"
                max="100"
                step="5"
                class="volume-slider"
                @input="updateVolume"
              />
              <span class="volume-value">{{ settings.alarmVolume }}%</span>
            </div>
          </div>
        </div>

        <!-- Manual Entry Settings -->
        <div class="settings-panel" v-if="activeTab === 'manual'">
          <div class="setting-group">
            <label>Add Manual Time Entry</label>
            <p class="setting-description">
              Add time manually for instances when you forgot to start the timer
              or couldn't use the system.
            </p>
          </div>

          <div class="setting-group">
            <label>Select Category</label>
            <div class="category-tabs">
              <button
                v-for="category in categories"
                :key="category"
                class="category-tab"
                :class="{ active: selectedCategory === category }"
                @click="
                  selectedCategory = category;
                  loadProjectsForCategory();
                "
              >
                {{ category }}
              </button>
            </div>
          </div>

          <div class="setting-group" v-if="selectedCategory">
            <label>Select Project</label>
            <select
              v-model="selectedProject"
              @change="loadTasksForProject"
              class="setting-select"
            >
              <option value="">Choose a project...</option>
              <option
                v-for="project in availableProjects"
                :key="project.id"
                :value="project"
              >
                {{ project.name }}
              </option>
            </select>
          </div>

          <div class="setting-group" v-if="selectedProject">
            <label>Select Task</label>
            <select v-model="selectedTask" class="setting-select">
              <option value="">Choose a task...</option>
              <option
                v-for="task in availableTasks"
                :key="task.id"
                :value="task"
              >
                {{ task.name }}
              </option>
            </select>
          </div>

          <div class="setting-group" v-if="selectedTask">
            <label>Date</label>
            <input
              type="date"
              v-model="manualEntryDate"
              class="setting-input"
              :max="today"
            />
          </div>

          <div class="setting-group" v-if="selectedTask">
            <label>Time Duration</label>
            <div class="time-input-group">
              <div class="time-input">
                <input
                  type="number"
                  v-model.number="manualHours"
                  min="0"
                  max="23"
                  class="setting-input time-field"
                  placeholder="0"
                />
                <span class="time-label">hours</span>
              </div>
              <div class="time-input">
                <input
                  type="number"
                  v-model.number="manualMinutes"
                  min="0"
                  max="59"
                  class="setting-input time-field"
                  placeholder="0"
                />
                <span class="time-label">minutes</span>
              </div>
            </div>
          </div>

          <div class="setting-group" v-if="selectedTask">
            <button
              class="add-time-button"
              @click="addManualTime"
              :disabled="!canAddTime"
            >
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
              </svg>
              Add Time Entry
            </button>
          </div>

          <div class="setting-group" v-if="manualEntryMessage">
            <div
              class="entry-message"
              :class="{
                success: manualEntrySuccess,
                error: !manualEntrySuccess,
              }"
            >
              {{ manualEntryMessage }}
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="save-button" @click="saveSettings">Save Settings</button>
        <button class="cancel-button" @click="$emit('close')">Cancel</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from "vue";

export default {
  name: "PomodoroSettingsModal",
  emits: ["close", "save"],
  setup(_, { emit }) {
    const activeTab = ref("timer");

    const settings = ref({
      pomodoroLength: 25,
      shortBreakLength: 5,
      longBreakLength: 15,
      longBreakAfter: 4,
      autoStartPomodoro: false,
      autoStartBreak: false,
      pomodoroAlarm: "beep",
      breakAlarm: "beep",
      alarmVolume: 50,
    });

    // Manual entry data
    const categories = ref(["DAILY", "WEEKLY", "MONTHLY", "COMPLETED"]);
    const selectedCategory = ref("");
    const selectedProject = ref("");
    const selectedTask = ref("");
    const availableProjects = ref([]);
    const availableTasks = ref([]);
    const manualEntryDate = ref(new Date().toISOString().split("T")[0]);
    const manualHours = ref(0);
    const manualMinutes = ref(0);
    const manualEntryMessage = ref("");
    const manualEntrySuccess = ref(false);
    const today = new Date().toISOString().split("T")[0];

    // Audio instances for alarm sounds
    const alarmSounds = {
      beep: null,
      guitar: null,
    };

    // Initialize audio files
    const initializeAudio = () => {
      try {
        // Create audio instances
        alarmSounds.beep = new Audio();
        alarmSounds.guitar = new Audio();

        // Load the audio files
        alarmSounds.beep.src = "/Alarm Beep.wav";
        alarmSounds.guitar.src = "/Guitar.wav";

        // Set initial volume
        Object.values(alarmSounds).forEach((audio) => {
          if (audio) {
            audio.volume = settings.value.alarmVolume / 100;
          }
        });
      } catch (error) {
        console.error("Error initializing audio:", error);
      }
    };

    // Input validation
    const validateInput = (field, event) => {
      const value = parseInt(event.target.value);
      const limits = {
        pomodoroLength: { min: 1, max: 120 },
        shortBreakLength: { min: 1, max: 60 },
        longBreakLength: { min: 1, max: 120 },
      };

      if (limits[field]) {
        const { min, max } = limits[field];
        if (value < min) {
          settings.value[field] = min;
        } else if (value > max) {
          settings.value[field] = max;
        }
      }
    };

    // Test alarm sound
    // Test alarm sound
    const testAlarm = (type) => {
      try {
        const alarmType =
          type === "pomodoro"
            ? settings.value.pomodoroAlarm
            : settings.value.breakAlarm;
        const audio = alarmSounds[alarmType];

        if (audio) {
          audio.currentTime = 0;
          audio.volume = settings.value.alarmVolume / 100;
          audio.play().catch((error) => {
            console.error("Error playing alarm:", error);
          });
        }
      } catch (error) {
        console.error("Error testing alarm:", error);
      }
    };
    // Update volume for all audio instances
    const updateVolume = () => {
      const volume = settings.value.alarmVolume / 100;
      Object.values(alarmSounds).forEach((audio) => {
        if (audio) {
          audio.volume = volume;
        }
      });
    };

    // Play alarm automatically (called from other components)
    const playAlarm = (eventType) => {
      try {
        const currentSettings = JSON.parse(
          localStorage.getItem("pomodoroSettings") || "{}"
        );
        const alarmType =
          eventType === "pomodoroEnd"
            ? currentSettings.pomodoroAlarm || "beep"
            : currentSettings.breakAlarm || "beep";
        const volume = (currentSettings.alarmVolume || 50) / 100;

        // Initialize audio if not already done
        if (!alarmSounds.beep && !alarmSounds.guitar) {
          initializeAudio();
        }

        const audio = alarmSounds[alarmType];
        if (audio) {
          audio.currentTime = 0;
          audio.volume = volume;
          audio.play().catch((error) => {
            console.error("Error playing automatic alarm:", error);
          });
        }
      } catch (error) {
        console.error("Error in automatic alarm:", error);
      }
    };

    // Load settings from database
    const loadSettings = async () => {
      try {
        const response = await fetch("/api/pomodoro/settings");
        const data = await response.json();
        if (data.success) {
          settings.value = { ...settings.value, ...data.settings };
        }
      } catch (error) {
        console.error("Error loading settings from database:", error);
        // Fallback to localStorage
        try {
          const saved = localStorage.getItem("pomodoroSettings");
          if (saved) {
            const savedSettings = JSON.parse(saved);
            settings.value = { ...settings.value, ...savedSettings };
          }
        } catch (fallbackError) {
          console.error(
            "Error loading settings from localStorage:",
            fallbackError
          );
        }
      }
    };

    // Manual entry functions
    const loadProjectsForCategory = async () => {
      if (!selectedCategory.value) return;

      try {
        const response = await fetch("/api/projects");
        const data = await response.json();
        if (data.success) {
          availableProjects.value = data.projects.filter(
            (project) => project.category === selectedCategory.value
          );
          selectedProject.value = "";
          selectedTask.value = "";
          availableTasks.value = [];
        }
      } catch (error) {
        console.error("Error loading projects:", error);
      }
    };

    const loadTasksForProject = async () => {
      if (!selectedProject.value || !selectedProject.value.id) return;

      try {
        const response = await fetch(
          `/api/projects/${selectedProject.value.id}/tasks`
        );
        const data = await response.json();
        if (data.success) {
          availableTasks.value = data.tasks;
          selectedTask.value = "";
        }
      } catch (error) {
        console.error("Error loading tasks:", error);
      }
    };

    const canAddTime = computed(() => {
      return (
        selectedTask.value &&
        manualEntryDate.value &&
        (manualHours.value > 0 || manualMinutes.value > 0)
      );
    });

    const addManualTime = async () => {
      if (!canAddTime.value) return;

      const totalSeconds = manualHours.value * 3600 + manualMinutes.value * 60;

      try {
        // Create manual session entry
        const sessionData = {
          taskId: selectedTask.value.id,
          projectId: selectedProject.value.id,
          duration: totalSeconds,
          completed: true, // Mark as completed since it's manual entry
          startedAt: new Date(
            manualEntryDate.value + "T12:00:00"
          ).toISOString(),
          completedAt: new Date(
            manualEntryDate.value + "T12:00:00"
          ).toISOString(),
        };

        const response = await fetch("/api/pomodoro/sessions", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(sessionData),
        });

        const data = await response.json();

        if (data.success) {
          manualEntrySuccess.value = true;
          manualEntryMessage.value = `Successfully added ${manualHours.value}h ${manualMinutes.value}m to "${selectedTask.value.name}" on ${manualEntryDate.value}`;

          // Reset form
          manualHours.value = 0;
          manualMinutes.value = 0;
          manualEntryDate.value = new Date().toISOString().split("T")[0];

          // Clear message after 5 seconds
          setTimeout(() => {
            manualEntryMessage.value = "";
          }, 5000);
        } else {
          manualEntrySuccess.value = false;
          manualEntryMessage.value =
            "Error adding manual time entry: " +
            (data.error || "Unknown error");
        }
      } catch (error) {
        console.error("Error adding manual time:", error);
        manualEntrySuccess.value = false;
        manualEntryMessage.value =
          "Error adding manual time entry: " + error.message;
      }
    };

    // Save settings to database and emit to parent
    const saveSettings = async () => {
      try {
        // Save to database
        const response = await fetch("/api/pomodoro/settings", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(settings.value),
        });
        const data = await response.json();

        if (data.success) {
          // Also save to localStorage for backward compatibility
          localStorage.setItem(
            "pomodoroSettings",
            JSON.stringify(settings.value)
          );
          emit("save", settings.value);
          emit("close");
        } else {
          console.error("Error saving settings to database:", data.error);
          // Fallback to localStorage only
          localStorage.setItem(
            "pomodoroSettings",
            JSON.stringify(settings.value)
          );
          emit("save", settings.value);
          emit("close");
        }
      } catch (error) {
        console.error("Error saving settings:", error);
        // Fallback to localStorage only
        try {
          localStorage.setItem(
            "pomodoroSettings",
            JSON.stringify(settings.value)
          );
          emit("save", settings.value);
          emit("close");
        } catch (fallbackError) {
          console.error(
            "Error saving settings to localStorage:",
            fallbackError
          );
        }
      }
    };

    onMounted(() => {
      loadSettings();
      initializeAudio();
    });

    return {
      activeTab,
      settings,
      saveSettings,
      validateInput,
      testAlarm,
      updateVolume,
      // Manual entry
      categories,
      selectedCategory,
      selectedProject,
      selectedTask,
      availableProjects,
      availableTasks,
      manualEntryDate,
      manualHours,
      manualMinutes,
      manualEntryMessage,
      manualEntrySuccess,
      today,
      loadProjectsForCategory,
      loadTasksForProject,
      canAddTime,
      addManualTime,
    };
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.modal-content {
  background: #2a2a2a;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  color: white;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #444;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.close-button svg {
  width: 20px;
  height: 20px;
}

.settings-content {
  display: flex;
  flex: 1;
  min-height: 400px;
  overflow: hidden;
}

.settings-nav {
  width: 200px;
  background: #333;
  padding: 16px 0;
}

.nav-item {
  width: 100%;
  background: none;
  border: none;
  color: #ccc;
  padding: 12px 20px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9rem;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.active {
  background: #ff4444;
  color: white;
}

.nav-item svg {
  width: 16px;
  height: 16px;
}

.settings-panel {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  padding-bottom: 80px; /* Add space for footer */
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #ddd;
}

.setting-select {
  width: 100%;
  background: #444;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
}

.setting-select:focus {
  outline: none;
  border-color: #ff4444;
}

/* Input with Select Styles */
.input-with-select {
  display: flex;
  gap: 8px;
  align-items: center;
}

.setting-input {
  flex: 1;
  background: #444;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 1rem;
  min-width: 80px;
}

.setting-input:focus {
  outline: none;
  border-color: #ff4444;
}

.setting-select-small {
  background: #444;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  min-width: 80px;
}

.setting-select-small:focus {
  outline: none;
  border-color: #ff4444;
}

.toggle-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-button {
  width: 50px;
  height: 26px;
  background: #555;
  border: none;
  border-radius: 13px;
  position: relative;
  cursor: pointer;
  transition: background 0.3s;
}

.toggle-button.active {
  background: #ff4444;
}

.toggle-slider {
  width: 22px;
  height: 22px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s;
}

.toggle-button.active .toggle-slider {
  transform: translateX(24px);
}

.radio-group {
  display: flex;
  gap: 24px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.9rem;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #666;
  border-radius: 50%;
  position: relative;
  transition: border-color 0.2s;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: #ff4444;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: "";
  width: 8px;
  height: 8px;
  background: #ff4444;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Alarm Options Styles */
.alarm-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.test-button {
  background: #555;
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 60px;
}

.test-button:hover {
  background: #666;
}

.test-button svg {
  width: 12px;
  height: 12px;
}

/* Volume Control Styles */
.volume-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.volume-icon {
  width: 20px;
  height: 20px;
  color: #ccc;
}

.volume-slider {
  flex: 1;
  height: 6px;
  background: #555;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #ff4444;
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #ff4444;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.volume-value {
  font-size: 0.9rem;
  color: #ccc;
  min-width: 40px;
  text-align: right;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #444;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.save-button {
  background: #ff4444;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
}

.save-button:hover {
  background: #e63939;
}

.cancel-button {
  background: #555;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
}

.cancel-button:hover {
  background: #666;
}

/* Manual Entry Styles */
.setting-description {
  color: #999;
  font-size: 14px;
  margin-top: 5px;
  line-height: 1.4;
}

.category-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-tab {
  background: #2a2a2a;
  border: 1px solid #444;
  color: #ccc;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.category-tab:hover {
  background: #333;
  border-color: #555;
}

.category-tab.active {
  background: #4a90e2;
  border-color: #4a90e2;
  color: white;
}

.time-input-group {
  display: flex;
  gap: 16px;
  align-items: center;
}

.time-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-field {
  width: 80px !important;
  text-align: center;
}

.time-label {
  color: #ccc;
  font-size: 14px;
  white-space: nowrap;
}

.add-time-button {
  background: #4a90e2;
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  width: fit-content;
}

.add-time-button:hover:not(:disabled) {
  background: #357abd;
  transform: translateY(-1px);
}

.add-time-button:disabled {
  background: #555;
  cursor: not-allowed;
  opacity: 0.6;
}

.add-time-button svg {
  width: 16px;
  height: 16px;
}

.entry-message {
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.entry-message.success {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.entry-message.error {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #f44336;
}
</style>
